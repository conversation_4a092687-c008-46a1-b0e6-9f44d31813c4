"use client";
import { ModalConstant } from "@/constants/ModalConstant";

import { useAppDispatch, useAppSelector } from "@/hooks";
import { modal } from "@/store/module/modal";
import {
  AccountCreatedSuccessModal,
  AllowanceModal,
  ChangePasswordModal,
  ContractTypeModal,
  CreateBranchModal,
  CreateEmployeeGroupModal,
  CreateEmployeeModal,
  CreateRoleModal,
  DepartmentModal,
  DesignationModal,
  DisbursePayrollModal,
  EmployeeBulkUploadModal,
  GradeModal,
  OnboardingOtpModal,
  PayrollUploadModal,
  RegionModal,
  SalaryPackageModal,
  SessionExpiredModal,
  TaxJurisdictionBulkUploadModal,
  TaxJurisdictionModal,
  UnitModal,
  UserModal,
} from "../modal";
import ConfirmationModal from "../modal/ConfirmationModal";
import DeductionModal from "../modal/DeductionModal";
import SubBranchModal from "../modal/SubBranchModal";
import BatchRecordTable from "../table/BatchRecordTable";
import { Dialog, DialogContent } from "../ui/dialog";

const ModalProvider = () => {
  const dispatch = useAppDispatch();

  const {
    modalOptions: { open, modalType, disableCloseOnBlur },
  } = useAppSelector((state) => state.modal);

  const renderModalContent = () => {
    switch (modalType) {
      case ModalConstant.accountCreatedSuccessModal:
        return <AccountCreatedSuccessModal />;
      case ModalConstant.onboardingOtpModal:
        return <OnboardingOtpModal />;
      case ModalConstant.createEmployeeModal:
        return <CreateEmployeeModal />;
      case ModalConstant.createEmployeeGroupModal:
        return <CreateEmployeeGroupModal />;
      case ModalConstant.createRoleModal:
        return <CreateRoleModal />;
      case ModalConstant.createBranchModal:
        return <CreateBranchModal />;
      case ModalConstant.gradeModal:
        return <GradeModal />;
      case ModalConstant.departmentModal:
        return <DepartmentModal />;
      case ModalConstant.unitModal:
        return <UnitModal />;
      case ModalConstant.contractTypeModal:
        return <ContractTypeModal />;
      case ModalConstant.designationModal:
        return <DesignationModal />;
      case ModalConstant.employeeBulkUploadModal:
        return <EmployeeBulkUploadModal />;
      case ModalConstant.userModal:
        return <UserModal />;
      case ModalConstant.sessionExpiredModal:
        return <SessionExpiredModal />;
      case ModalConstant.salaryPackageModal:
        return <SalaryPackageModal />;
      case ModalConstant.allowanceModal:
        return <AllowanceModal />;
      case ModalConstant.deductionModal:
        return <DeductionModal />;
      case ModalConstant.payroll:
        return <PayrollUploadModal />;
      case ModalConstant.batchRecord:
        return <BatchRecordTable />;
      case ModalConstant.confirmationModal:
        return <ConfirmationModal />;
      case ModalConstant.taxJurisdictionModal:
        return <TaxJurisdictionModal />;
      case ModalConstant.taxJurisdictionBulkUploadModal:
        return <TaxJurisdictionBulkUploadModal />;
      case ModalConstant.regionModal:
        return <RegionModal />;
      case ModalConstant.subBranchModal:
        return <SubBranchModal />;
      case ModalConstant.changePasswordModal:
        return <ChangePasswordModal />;
      case ModalConstant.disbursePayrollRecordModal:
        return <DisbursePayrollModal />;

      default:
        return null;
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        if (!disableCloseOnBlur && !isOpen) {
          dispatch(modal.mutation.close());
        }
      }}
    >
      <DialogContent className=" md:max-w-3xl max-w-[95%] min-[500px]:w-auto  max-h-[calc(100vh-4rem)] rounded-md overflow-y-scroll !pt-0">
        {renderModalContent()}
      </DialogContent>
    </Dialog>
  );
};

export default ModalProvider;
