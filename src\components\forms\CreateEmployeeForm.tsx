import { stateLga } from "@/data/state-lga";
import { useAppDispatch } from "@/hooks";
import {
  CreateEmployeeDef,
  CreateEmployeeSchema,
} from "@/models/validations/employee/create-employee.validation";
import { useGetBranchesQuery } from "@/services/branch.service";
import { useGetContractTypesQuery } from "@/services/contract-type.service";
import { useGetDepartmentQuery } from "@/services/department.service";
import { useGetDesignationsQuery } from "@/services/designation.service";
import { useGetGradeLevelsQuery } from "@/services/employee-group.service";
import { useCreateEmployeeMutation } from "@/services/employee.service";
import { useGetGradeQuery } from "@/services/grade.service";
import {
  useVerifyBvnMutation,
  useVerifyNinMutation,
} from "@/services/kyc.service";
import { useGetSalaryPackagesQuery } from "@/services/salary-package.service";
import { useGetSubBranchesQuery } from "@/services/sub-branch.service";
import { modal } from "@/store/module/modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { formatDate } from "date-fns";
import { LoaderIcon } from "lucide-react";
import { useState } from "react";
import { FieldPath, useForm } from "react-hook-form";
import { toast } from "sonner";
import { ComboBoxField, InputField } from "../form-fields";
import ComboboxField from "../form-fields/ComboBoxField";
import DateField from "../form-fields/DateField";
import PhoneInputField from "../form-fields/PhoneInputField";
import { Button } from "../ui/button";
import { Form } from "../ui/form";

const CreateEmployeeForm = () => {
  const dispatch = useAppDispatch();

  const localPrevData = localStorage.getItem("emp");

  const prevData =
    localPrevData && (JSON.parse(localPrevData) as CreateEmployeeDef);
  const [createEmployee, { isLoading: isCreatingEmployee, error }] =
    useCreateEmployeeMutation();

  const [verifyNin, { isLoading: isVerifyingNin }] = useVerifyNinMutation();

  const [verifyBvn, { isLoading: isVerifyingBvn }] = useVerifyBvnMutation();

  const { data: gradeLevelresponse, isLoading: isLoadingGradeLevel } =
    useGetGradeLevelsQuery(undefined, {
      refetchOnMountOrArgChange: true,
    });

  const { data: designationResponse, isLoading: isLoadingDesignation } =
    useGetDesignationsQuery(undefined, {
      refetchOnMountOrArgChange: true,
    });

  const { data: subBranchesResponse, isLoading: isLoadingSubBranches } =
    useGetSubBranchesQuery(undefined, {
      refetchOnMountOrArgChange: true,
    });

  const { data: salaryPackageResponse, isLoading: isLoadingSalaryPackage } =
    useGetSalaryPackagesQuery(undefined, {
      refetchOnMountOrArgChange: true,
    });
  const { data: gradeResponse, isLoading: isLoadingGrade } = useGetGradeQuery(
    undefined,
    {
      refetchOnMountOrArgChange: true,
    }
  );

  const { data: departmentResponse, isLoading: isLoadingDepartMent } =
    useGetDepartmentQuery(undefined, {
      refetchOnMountOrArgChange: true,
    });

  const { data: branchresponse, isLoading: isLoadingBranch } =
    useGetBranchesQuery(undefined, {
      refetchOnMountOrArgChange: true,
    });

  const { data: contractTypeResponse, isLoading: isLoadingContractType } =
    useGetContractTypesQuery(undefined, {
      refetchOnMountOrArgChange: true,
    });

  const [currentStep, setCurrentStep] = useState(0);
  const [kycValidation, setKycValidation] = useState({
    bvnValidated: false,
    ninValidated: false,
  });

  const form = useForm<CreateEmployeeDef>({
    resolver: zodResolver(CreateEmployeeSchema),
    defaultValues: {
      ...prevData,
      birthday: prevData
        ? prevData.birthday
          ? new Date(prevData.birthday)
          : new Date()
        : undefined,
      dateAppointedToLevel: prevData
        ? prevData.dateAppointedToLevel
          ? new Date(prevData.dateAppointedToLevel)
          : new Date()
        : undefined,
      dateOfGraduation: prevData
        ? prevData.dateOfGraduation
          ? new Date(prevData.dateOfGraduation)
          : new Date()
        : undefined,
      dateEmployed: prevData
        ? prevData.dateEmployed
          ? new Date(prevData.dateEmployed)
          : new Date()
        : undefined,
      residentialCountry: "Nigeria",
      staffCode: "",
    },
    mode: "all",
  });

  const state = form.watch("stateOfOrigin");
  const residenceState = form.watch("residentialState");

  const bvn = form.watch("bvn");
  const nin = form.watch("nin");
  const lastname = form.watch("lastName");
  const firstname = form.watch("firstName");

  const handleStepChange = async ({
    step,
    fields,
  }: {
    step: number;
    fields?: FieldPath<CreateEmployeeDef>[];
  }) => {
    if (step < 0) {
      return;
    }
    if (step > currentStep) {
      const isValid = await form.trigger(fields);

      if (!isValid) {
        return;
      }
    }
    localStorage.setItem("emp", JSON.stringify(form.getValues()));
    setCurrentStep(step);
  };

  const onSubmit = async (data: CreateEmployeeDef) => {
    try {
      const res = await createEmployee({
        ...data,
        dateOfGraduation: data.dateOfGraduation
          ? formatDate(data.dateOfGraduation, "dd/MM/yyyy")
          : "",
        dateAppointedToLevel: data.dateAppointedToLevel
          ? formatDate(data.dateAppointedToLevel, "dd/MM/yyyy")
          : "",
        dateEmployed: data.dateEmployed
          ? formatDate(data.dateEmployed, "dd/MM/yyyy")
          : "",
        birthday: data.birthday ? formatDate(data.birthday, "dd/MM/yyyy") : "",
        phone1: data.phone1.replace(/\D/g, ""),
      }).unwrap();

      if (res.success) {
        toast("Request successfull and pending authorization");
        localStorage.removeItem("emp");
        dispatch(modal.mutation.close());
      }
    } catch {
      console.log(error);
    }
  };

  const onVerifyBiodata = async () => {
    handleStepChange({
      step: 2,
      fields: [
        "firstName",
        "lastName",
        "middleName",
        "phone1",
        "phone2",
        "residentialState",
        "residentialState",
        "residentialLocalGovt",
        "birthday",
        "religion",
        "gender",
        "stateOfOrigin",
        "localGovt",
        "placeOfBirth",
        "email",
      ],
    });
  };

  const handleVerification = async () => {
    const isValid = !kycValidation.bvnValidated
      ? await form.trigger(["firstName", "lastName", "bvn"])
      : await form.trigger(["firstName", "lastName", "nin"]);

    console.log(isValid, kycValidation);

    if (isValid && !kycValidation.bvnValidated) {
      const res = await verifyBvn({ firstname, lastname, bvn: Number(bvn) });

      if (res.data?.success) {
        form.setValue("firstName", res.data.data.applicant.firstname);
        form.setValue("lastName", res.data.data.bvn.lastname);
        form.setValue("middleName", res.data.data.bvn.middlename);
        form.setValue("middleName", res.data.data.bvn.middlename);
        form.setValue("bvn", res.data.data.bvn.bvn);
        form.setValue("phone1", res.data.data.bvn.phone);

        setKycValidation({ ...kycValidation, bvnValidated: true });
        localStorage.setItem("emp", JSON.stringify(form.getValues()));
      }
    } else if (isValid && !kycValidation.ninValidated) {
      const res = await verifyNin({ firstname, lastname, nin: Number(nin) });

      if (res.data?.success) {
        form.setValue("firstName", res.data.data.applicant.firstname);
        form.setValue(
          "residentialAddress",
          res.data.data.nin.residence.address1
        );
        form.setValue("residentialState", res.data.data.nin.residence.state);
        form.setValue("residentialLocalGovt", res.data.data.nin.residence.lga);
        setKycValidation({ ...kycValidation, ninValidated: true });
        setCurrentStep(1);
        localStorage.setItem("emp", JSON.stringify(form.getValues()));
      }
    }
  };

  const maritalStatus = form.watch("maritalStatus");

  return (
    <section className="max-w-md mx-auto min-[500px]:min-w-md p-2">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 ">
          <div className="space-y-4">
            {currentStep === 0 && (
              <div className="space-y-4">
                <p className="font-semibold text-primary text-sm">
                  1. KYC VERIFICATION
                </p>

                <InputField
                  form={form}
                  name="firstName"
                  label="First Name"
                  placeholder="Enter your first name"
                  disabled={currentStep > 1}
                />
                <InputField
                  form={form}
                  name="lastName"
                  label="Last Name"
                  placeholder="Enter your last name"
                  disabled={currentStep > 1}
                />

                <InputField
                  form={form}
                  name="bvn"
                  label="BVN"
                  maxLength={11}
                  formatAsNumber
                  placeholder="Enter your employee BVN"
                  type="password"
                />

                {kycValidation.bvnValidated && (
                  <InputField
                    form={form}
                    name="nin"
                    label="NIN"
                    maxLength={11}
                    formatAsNumber
                    placeholder="Enter your employee NIN"
                    type="password"
                  />
                )}
                <div className="flex gap-4 justify-between">
                  <Button
                    className=" font-medium"
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setCurrentStep(1);
                    }}
                    disabled
                  >
                    Prev
                  </Button>
                  <Button
                    className=" font-medium"
                    type="button"
                    onClick={handleVerification}
                    disabled={isVerifyingBvn || isVerifyingNin}
                  >
                    {(isVerifyingBvn || isVerifyingNin) && (
                      <LoaderIcon className="animate-spin" />
                    )}
                    {`Verify ${
                      kycValidation.bvnValidated
                        ? "NIN"
                        : kycValidation.ninValidated
                        ? "Proceed"
                        : "BVN"
                    }`}
                  </Button>
                </div>
              </div>
            )}
            {currentStep === 1 && (
              <>
                <p className="font-semibold text-primary text-sm">
                  2. EMPLOYEE BIODATA
                </p>
                <InputField form={form} name="email" label="Email" />
                <div className="flex  items-start gap-x-4">
                  <InputField
                    form={form}
                    name="middleName"
                    label="Middle Name"
                  />
                  <ComboBoxField
                    options={[
                      {
                        name: "Islam",
                      },
                      {
                        name: "Christainity",
                      },
                      {
                        name: "Others",
                      },
                    ]}
                    form={form}
                    labelKey="name"
                    valueKey="name"
                    name="religion"
                    label="Religion"
                    placeholder="Select Religion"
                  />
                </div>
                <div className="flex items-start gap-4">
                  <ComboBoxField
                    options={[
                      {
                        name: "Male",
                      },
                      {
                        name: "Female",
                      },
                    ]}
                    form={form}
                    name="gender"
                    label="Gender"
                    labelKey="name"
                    valueKey="name"
                  />
                  <DateField
                    form={form}
                    name="birthday"
                    label="Date of birth"
                  />
                </div>
                <div className="flex items-start gap-4">
                  <ComboboxField
                    form={form}
                    options={Object.keys(stateLga).map((state) => ({
                      value: state,
                      label: state,
                    }))}
                    name="residentialState"
                    label="Residence State"
                  />
                  <ComboboxField
                    form={form}
                    options={
                      residenceState && residenceState in stateLga
                        ? stateLga[residenceState as keyof typeof stateLga].map(
                            (lga) => ({
                              value: lga,
                              label: lga,
                            })
                          )
                        : []
                    }
                    name="residentialLocalGovt"
                    label="Residence LGA"
                  />
                </div>
                <InputField
                  form={form}
                  name="residentialAddress"
                  label="Residence Address"
                />
                <InputField
                  form={form}
                  name="residentialCountry"
                  label="Residence Country"
                  readOnly
                />
                <InputField
                  form={form}
                  name="phone1"
                  label="Phone Number Line 1"
                />
                <PhoneInputField
                  form={form}
                  name="phone2"
                  label="Phone Number Line 2"
                />

                <ComboboxField
                  form={form}
                  options={Object.keys(stateLga).map((state) => ({
                    value: state,
                    label: state,
                  }))}
                  name="placeOfBirth"
                  label="Place of Birth"
                  placeholder="Select State of Birth"
                />
                <ComboboxField
                  form={form}
                  options={Object.keys(stateLga).map((state) => ({
                    value: state,
                    label: state,
                  }))}
                  name="stateOfOrigin"
                  label="State of Origin"
                  placeholder="Select State"
                />

                <ComboboxField
                  form={form}
                  options={
                    state && state in stateLga
                      ? stateLga[state as keyof typeof stateLga].map((lga) => ({
                          value: lga,
                          label: lga,
                        }))
                      : []
                  }
                  name="localGovt"
                  label="Local Government Area"
                  placeholder="Select LGA"
                />
                <div className="flex gap-4 justify-between">
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => setCurrentStep(currentStep - 1)}
                  >
                    Prev
                  </Button>
                  <Button
                    className=" font-medium"
                    type="button"
                    onClick={onVerifyBiodata}
                  >
                    Proceed
                  </Button>
                </div>
              </>
            )}
            {currentStep === 2 && (
              <>
                <p className="font-semibold text-primary text-sm">
                  3. EMPLOYEE MARITAL STATUS
                </p>
                <div className="flex items-start gap-4">
                  <ComboboxField
                    form={form}
                    options={[
                      { value: "single", label: "Single" },
                      { value: "married", label: "Married" },
                      { value: "divorced", label: "Divorced" },
                      { value: "widowed", label: "Widowed" },
                      { value: "separated", label: "Separated" },
                      { value: "engaged", label: "Engaged" },
                      {
                        value: "in_a_relationship",
                        label: "In a Relationship",
                      },
                    ]}
                    name="maritalStatus"
                    label="Marital Status"
                    placeholder="Marital Status"
                  />

                  {maritalStatus && maritalStatus !== "single" && (
                    <InputField
                      form={form}
                      name="noOfChildren"
                      label="Number of Children"
                      placeholder="Number of Children"
                      formatAsNumber
                    />
                  )}
                </div>
                {maritalStatus && maritalStatus !== "single" && (
                  <InputField
                    form={form}
                    name="nameOfSpouse"
                    label="Spouse Name"
                  />
                )}
                <div className="flex gap-4 justify-between">
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => setCurrentStep(currentStep - 1)}
                  >
                    Prev
                  </Button>
                  <Button
                    className=" font-medium"
                    type="button"
                    onClick={() =>
                      handleStepChange({
                        step: 3,
                        fields: [
                          "noOfChildren",
                          "nameOfSpouse",
                          "maritalStatus",
                        ],
                      })
                    }
                  >
                    Proceed
                  </Button>
                </div>
              </>
            )}
            {currentStep === 3 && (
              <>
                <p className="font-semibold text-primary text-sm">
                  4. NEXT OF KIN
                </p>
                <InputField
                  form={form}
                  name="nextOfKinFullName"
                  label="Full Name"
                />
                <InputField
                  form={form}
                  name="nextOfKinRelationship"
                  label="Relationship"
                />
                <PhoneInputField
                  form={form}
                  name="nextOfKinPhoneNumber"
                  label="Phone Number"
                />
                <InputField
                  form={form}
                  name="nextOfKinEmail"
                  label="Email"
                  optional
                />
                <InputField
                  form={form}
                  name="nextOfKinAddress"
                  label="Residential Address"
                />

                <div className="flex gap-4 justify-between">
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => setCurrentStep(currentStep - 1)}
                  >
                    Prev
                  </Button>
                  <Button
                    className="font-medium"
                    type="button"
                    onClick={() =>
                      handleStepChange({
                        step: 4,
                        fields: [
                          "nextOfKinAddress",
                          "nextOfKinEmail",
                          "nextOfKinFullName",
                          "nextOfKinPhoneNumber",
                          "nextOfKinRelationship",
                        ],
                      })
                    }
                  >
                    Proceed
                  </Button>
                </div>
              </>
            )}
            {currentStep === 4 && (
              <>
                <p className="font-semibold text-primary text-sm">
                  5. EDUCATION DETAILS
                </p>
                <ComboBoxField
                  options={[
                    {
                      label: "First School Leaving Certificate (FSLC)",
                      value: "FSLC",
                    },
                    {
                      label: "Senior Secondary School Certificate (SSCE)",
                      value: "SSCE",
                    },
                    { label: "National Diploma (ND)", value: "ND" },
                    { label: "Higher National Diploma (HND)", value: "HND" },
                    {
                      label: "Bachelor’s Degree (B.Sc/B.A/B.Eng)",
                      value: "B.Sc",
                    },
                    { label: "Postgraduate Diploma (PGD)", value: "PGD" },
                    { label: "Master’s Degree (M.Sc/M.A)", value: "M.Sc" },
                    { label: "Doctorate Degree (Ph.D)", value: "Ph.D" },
                    { label: "Other", value: "Other" },
                  ]}
                  form={form}
                  name="highestQualification"
                  label="Highest Qualification"
                />
                <InputField
                  form={form}
                  name="institutionName"
                  label="Institution Name"
                />
                <InputField form={form} name="course" label="Course" />

                <DateField
                  form={form}
                  label="Date of Graduation"
                  name="dateOfGraduation"
                />

                <div className="flex gap-4 justify-between">
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => setCurrentStep(currentStep - 1)}
                  >
                    Prev
                  </Button>
                  <Button
                    className="font-medium"
                    type="button"
                    onClick={() =>
                      handleStepChange({
                        step: 5,
                        fields: [
                          "highestQualification",
                          "institutionName",
                          "course",
                          "dateOfGraduation",
                        ],
                      })
                    }
                  >
                    Proceed / Skip
                  </Button>
                </div>
              </>
            )}
            {currentStep === 5 && (
              <>
                <p className="font-semibold text-primary text-sm">
                  5. GUARANTOR DETAILS
                </p>
                <InputField
                  form={form}
                  name="guarantorFullname"
                  label="Full Name"
                />
                <InputField
                  form={form}
                  name="guarantorRelationShip"
                  label="Relationship"
                />
                <PhoneInputField
                  form={form}
                  name="guarantorPhoneNumber"
                  label="Phone Number"
                />
                <InputField
                  form={form}
                  name="guarantorOccupation"
                  label="Occupation / Profession"
                />
                <InputField
                  form={form}
                  name="guarantorAddress"
                  label="Residential Address"
                />

                <div className="flex gap-4 justify-between">
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => setCurrentStep(currentStep - 1)}
                  >
                    Prev
                  </Button>
                  <Button
                    className="font-medium"
                    type="button"
                    onClick={() =>
                      handleStepChange({
                        step: 6,
                        fields: [
                          "guarantorFullname",
                          "guarantorRelationShip",
                          "guarantorPhoneNumber",
                          "guarantorOccupation",
                          "guarantorAddress",
                        ],
                      })
                    }
                  >
                    Proceed / Skip
                  </Button>
                </div>
              </>
            )}
            {currentStep === 6 && (
              <>
                <p className="font-semibold text-primary text-sm">
                  6. EMPLOYMENT DETAILS
                </p>
                <div className="flex flex-col items-start gap-4">
                  <ComboboxField
                    isLoading={isLoadingGradeLevel}
                    options={[
                      "Mr",
                      "Mrs",
                      "Miss",
                      "Ms",
                      "Dr",
                      "Prof",
                      "Engr",
                      "Rev",
                    ].map((tle) => ({
                      label: tle,
                      value: tle,
                    }))}
                    labelKey="label"
                    valueKey="value"
                    form={form}
                    name="title"
                    label="Title"
                  />
                  <InputField
                    form={form}
                    name="nationality"
                    label="Nationality"
                  />
                  <InputField
                    form={form}
                    name="staffCode"
                    label="Staff ID"
                    optional
                  />

                  <ComboboxField
                    isLoading={isLoadingDesignation}
                    options={
                      designationResponse?.data
                        ? designationResponse.data.map((designation) => ({
                            label: designation.name,
                            value: designation.name,
                          }))
                        : []
                    }
                    labelKey="label"
                    valueKey="value"
                    form={form}
                    name="jobTitleName"
                    label="Designation"
                  />
                  <ComboboxField
                    isLoading={isLoadingContractType}
                    options={
                      contractTypeResponse?.data
                        ? contractTypeResponse.data.map((contractType) => ({
                            label: contractType.name,
                            value: contractType.name,
                          }))
                        : []
                    }
                    labelKey="label"
                    valueKey="value"
                    form={form}
                    name="contractTypeName"
                    label="Employment Type"
                  />
                  <ComboboxField
                    isLoading={isLoadingGrade}
                    options={
                      gradeResponse?.data
                        ? gradeResponse.data.map((grade) => ({
                            label: `${grade.name}`,
                            value: grade.name,
                          }))
                        : []
                    }
                    labelKey="label"
                    valueKey="value"
                    form={form}
                    name="jobGradeName"
                    label="Cadre"
                  />
                  <ComboboxField
                    isLoading={isLoadingGradeLevel}
                    options={
                      gradeLevelresponse?.data
                        ? gradeLevelresponse.data.map((gl) => ({
                            label: `${gl.name}`,
                            value: gl.name,
                          }))
                        : []
                    }
                    labelKey="label"
                    valueKey="value"
                    form={form}
                    name="gradeLevelName"
                    label="Grade Level"
                  />
                  <ComboboxField
                    isLoading={isLoadingSalaryPackage}
                    options={
                      salaryPackageResponse?.data
                        ? salaryPackageResponse.data.map((sp) => ({
                            label: `${sp.name} `,
                            value: sp.name,
                          }))
                        : []
                    }
                    labelKey="label"
                    valueKey="value"
                    form={form}
                    name="salaryPackageName"
                    label="Salary Package"
                  />
                  <ComboboxField
                    isLoading={isLoadingBranch}
                    options={
                      branchresponse?.data
                        ? branchresponse.data.map((branch) => ({
                            label: `${branch.name} | ${branch.taxJurisdiction}`,
                            value: branch.name,
                          }))
                        : []
                    }
                    labelKey="label"
                    valueKey="value"
                    form={form}
                    name="branchName"
                    label="Branch/Unit"
                  />
                  <ComboboxField
                    isLoading={isLoadingSubBranches}
                    options={
                      subBranchesResponse?.data
                        ? subBranchesResponse.data.map((sBranch) => ({
                            label: `${sBranch.name}`,
                            value: sBranch.name,
                          }))
                        : []
                    }
                    labelKey="label"
                    valueKey="value"
                    form={form}
                    name="subBranchName"
                    label="Sub Branch"
                  />
                  <ComboboxField
                    isLoading={isLoadingDepartMent}
                    options={
                      departmentResponse?.data
                        ? departmentResponse.data.map((department) => ({
                            label: `${department.name}`,
                            value: department.name,
                          }))
                        : []
                    }
                    labelKey="label"
                    valueKey="value"
                    form={form}
                    name="departmentName"
                    label="Department"
                  />
                  <DateField
                    form={form}
                    label="Date employed"
                    name="dateEmployed"
                  />
                  <DateField
                    form={form}
                    label="Date appointed to level"
                    name="dateAppointedToLevel"
                  />
                  <InputField form={form} name="taxId" label="PAYE" />
                  <InputField form={form} name="pensionId" label="Pension ID" />
                  <InputField form={form} name="pfa" label="PFA" />
                </div>

                <div className="flex gap-4 justify-between">
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => setCurrentStep(currentStep - 1)}
                  >
                    Prev
                  </Button>
                  <Button className="font-medium" disabled={isCreatingEmployee}>
                    {isCreatingEmployee && (
                      <LoaderIcon className="animate-spin" />
                    )}
                    Create Employee
                  </Button>
                </div>
              </>
            )}
          </div>
        </form>
      </Form>
    </section>
  );
};

export default CreateEmployeeForm;
